<template>
  <div class="roundup-game-pages-layout">
    <router-view
      :game-session-id="gameSessionId"
      :property-uuid="propertyUuid"
      :game-communities-details="gameCommunitiesDetails"
      :shareable-results-url="shareableResultsUrl"
      :is-current-user-session="isCurrentUserSession"
      :game-title="gameTitle"
      :game-default-currency="gameDefaultCurrency"
      :total-properties="totalProperties"
      :first-prop-listing="firstPropListing"
      :realty-game-summary="realtyGameSummary"
      :is-loading="isLoading"
      :error="error"
      :results="results"
      :player-results="playerResults"
      :ss-game-session="ssGameSession"
      :comparison-summary="comparisonSummary"
      :game-breakdown="gameBreakdown"
      :overall-ranking="overallRanking"
      :leaderboard="leaderboard"
      :show-leaderboard="showLeaderboard"
      :get-score-color="getScoreColor"
      :format-price-with-both-currencies="formatPriceWithBothCurrencies"
      @load-results="handleLoadResults"
      @update-progress="handleProgressUpdate"
      @game-complete="handleGameComplete"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useQuasar } from 'quasar'
import { useRealtyGame } from 'src/concerns/realty-game/composables/useRealtyGame'
import { useRealtyGameStorage } from 'src/concerns/realty-game/composables/useRealtyGameStorage'
import { useServerRealtyGameResults } from 'src/concerns/realty-game/composables/useServerRealtyGameResults'
import { useCurrencyConverter } from 'src/concerns/realty-game/composables/useCurrencyConverter'

const $route = useRoute()
const $router = useRouter()
const $q = useQuasar()

// Initialize composables
const {
  totalProperties,
  gameTitle,
  realtyGameSummary,
  gameCommunitiesDetails,
  firstPropListing,
  fetchPriceGuessData,
  gameDesc,
  gameDefaultCurrency,
} = useRealtyGame()

const { getCurrentSessionId, getCurrencySelection } = useRealtyGameStorage()

const {
  isLoading,
  error,
  results,
  playerResults,
  comparisonSummary,
  gameBreakdown,
  ssGameSession,
  fetchResults,
  getScoreColor,
} = useServerRealtyGameResults()

const { setCurrency, formatPriceWithBothCurrencies } = useCurrencyConverter()

// Computed properties
const gameSessionId = computed(
  () => $route.query.session || $route.params.gameSessionId || ''
)

const propertyUuid = computed(() => $route.params.propertyUuid || '')

const shareableResultsUrl = computed(() => {
  if (!gameSessionId.value || !$route.params.gameSlug) {
    return ''
  }
  let shareRoute = {
    name: 'rRoundupGameResultsShareable',
    params: {
      gameSlug: $route.params.gameSlug,
      propertyUuid: propertyUuid.value,
      gameSessionId: gameSessionId.value,
    },
  }
  let fullPath = `${location.origin}${$router.resolve(shareRoute).href}`
  return fullPath
})

const isCurrentUserSession = computed(() => {
  const currentSessionId = getCurrentSessionId()
  return currentSessionId === gameSessionId.value
})

// Results-specific computed properties
const overallRanking = computed(() => results.value?.overall_ranking || null)
const leaderboard = computed(() => results.value?.leaderboard || [])
const showLeaderboard = computed(() => {
  return leaderboard.value && leaderboard.value.length > 1
})

// Event handlers
const handleLoadResults = async () => {
  if (gameSessionId.value && $route.params.gameSlug) {
    await fetchResults(gameSessionId.value, $route.params.gameSlug)
  }
}

const handleProgressUpdate = (data) => {
  // Handle progress updates from child components
  console.log('Progress update:', data)
}

const handleGameComplete = (sessionId) => {
  // Navigate to results page
  $router.push({
    name: 'rRoundupGameResultsSummary',
    params: {
      gameSlug: $route.params.gameSlug,
      propertyUuid: propertyUuid.value,
      gameSessionId: sessionId,
    },
  })
}

// Initialize currency from session
const initializeCurrency = () => {
  const sessionCurrency = getCurrencySelection(gameSessionId.value)
  if (sessionCurrency) {
    setCurrency(sessionCurrency)
  }
}

// Initialize game data
const initializeGame = async () => {
  try {
    if ($route.params.gameSlug) {
      await fetchPriceGuessData($route.params.gameSlug)
    }
  } catch (err) {
    console.error('Failed to load game data:', err)
    $q.notify({
      color: 'negative',
      message: 'Failed to load game data',
      icon: 'error',
    })
  }
}

// Lifecycle
onMounted(async () => {
  await initializeGame()
  initializeCurrency()
  
  // Load results if we're on a results page
  const resultsRoutes = [
    'rRoundupGameResultsSummary',
    'rRoundupGameResultsShareable',
    'rRoundupGameResultsDetailed'
  ]
  
  if (resultsRoutes.includes($route.name) && gameSessionId.value) {
    await handleLoadResults()
  }
})

// Watch for route changes to reload results
watch(
  () => [$route.name, gameSessionId.value, $route.params.gameSlug],
  async ([routeName, sessionId, gameSlug]) => {
    const resultsRoutes = [
      'rRoundupGameResultsSummary',
      'rRoundupGameResultsShareable',
      'rRoundupGameResultsDetailed'
    ]
    
    if (resultsRoutes.includes(routeName) && sessionId && gameSlug) {
      await handleLoadResults()
    }
  }
)
</script>

<style scoped>
.roundup-game-pages-layout {
  min-height: 100vh;
}
</style>
